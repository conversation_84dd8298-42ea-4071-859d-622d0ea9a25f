// routes/common/subscriptionRoutes.js
import express from 'express';
import { asyncHandler } from '../../middleware/errorHandler.js';
import {
    getPublicSubscriptionPlans,
    getPublicSubscriptionPlan
} from '../../controllers/common/subscriptionController.js';

const router = express.Router();

/**
 * Public subscription plan routes
 * These routes are accessible to all users without authentication
 * Used for displaying pricing information on the frontend
 */

// Get all active subscription plans for public display
router.get('/plans', asyncHandler(getPublicSubscriptionPlans));

// Get specific subscription plan by name for public access
router.get('/plans/:planName', asyncHandler(getPublicSubscriptionPlan));

export default router;

import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../../context/AuthContext';

// Component Imports
import PricingCard from '../../../components/common/price/PricingCard';
import PageBackground from '../../../components/common/price/PageBackground';
import PaymentModal from '../../../components/common/price/PaymentModal';
import useSubscriptionPlans from '../../../hooks/useSubscriptionPlans';

// Dynamic pricing plans are now loaded via useSubscriptionPlans hook

// --- Inner component for rendering the pricing cards and header ---
// This component remains unchanged as its logic was correct.
const PricingView = ({
    pricingTiers,
    onPlanSelect,
    isViewLoading,
    pageMessage,
    currentPlanName,
    isCardActionLoading
}) => {
    if (isViewLoading) {
        return (
            <div className="min-h-[60vh] flex flex-col items-center justify-center text-center p-4">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-sky-400 mb-4"></div>
                <p className="text-lg text-sky-300">Loading available plans...</p>
            </div>
        );
    }
     if ((!pricingTiers || pricingTiers.length === 0) && !(pageMessage?.text && pageMessage.type === 'error')) {
         return (
            <div className="min-h-[60vh] flex flex-col items-center justify-center text-center p-4">
                <h2 className="text-2xl font-semibold text-slate-200 mb-2">No Plans Available</h2>
                <p className="text-slate-400 max-w-md">
                    No pricing plans are currently configured. Please check back later.
                </p>
            </div>
        );
    }
    return (
        <div className="relative z-10 flex flex-col items-center justify-center min-h-screen p-4 sm:p-6 lg:p-8">
            <div className="text-center mb-12 mt-10 md:mt-16">
                <h1 className="text-4xl sm:text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 mb-4">
                    Choose Your Plan
                </h1>
                <p className="text-lg text-slate-300 max-w-2xl mx-auto">
                    Unlock powerful features and take your productivity to the next level.
                </p>
            </div>
            {pageMessage && pageMessage.text && (
                <p className={`text-center text-sm my-4 p-3 rounded-md border w-full max-w-lg mx-auto ${
                    pageMessage.type === 'error' ? 'bg-red-500/10 border-red-500/30 text-red-300' :
                    pageMessage.type === 'success' ? 'bg-green-500/10 border-green-500/30 text-green-300' :
                    'bg-sky-500/10 border-sky-500/30 text-sky-300'
                }`}>
                    {pageMessage.text}
                </p>
            )}
            {pricingTiers && pricingTiers.length > 0 && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 xl:gap-8 max-w-sm md:max-w-4xl lg:max-w-6xl w-full items-stretch mb-12">
                    {pricingTiers.map((tier) => (
                        <PricingCard
                            key={tier.id || tier.tierName}
                            {...tier}
                            onButtonClick={() => onPlanSelect(tier)}
                            disabled={isCardActionLoading || (currentPlanName === tier.tierName && tier.actionType === 'purchase')}
                            isCurrentPlan={currentPlanName === tier.tierName}
                        />
                    ))}
                </div>
            )}
        </div>
    );
};
PricingView.displayName = 'PricingView';


// --- Main Exported Component: PricingPage (Content part) ---
const PricingPage = () => {
    const [isPageLoading, setIsPageLoading] = useState(false);
    const [isCardActionLoading, setIsCardActionLoading] = useState(false);
    const [pageMessage, setPageMessage] = useState({ text: '', type: '' });

    // State specifically for the payment modal
    const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
    const [selectedPlanForModal, setSelectedPlanForModal] = useState(null);

    const auth = useAuth();
    const navigate = useNavigate();

    // Use dynamic subscription plans hook
    const {
        plans: pricingTiers,
        loading: isLoadingPlans,
        error: plansError,
        sseConnected,
        refresh: refreshPlans
    } = useSubscriptionPlans();

    // Handle plans loading error
    useEffect(() => {
        if (plansError) {
            setPageMessage({
                text: `Failed to load pricing plans: ${plansError}`,
                type: 'error'
            });
        } else if (pricingTiers.length === 0 && !isLoadingPlans) {
            setPageMessage({
                text: 'No pricing plans are currently configured.',
                type: 'info'
            });
        } else {
            setPageMessage({ text: '', type: '' });
        }
    }, [plansError, pricingTiers.length, isLoadingPlans]);

    // **FIXED**: The handler for selecting a plan
    const handlePlanSelect = useCallback((plan) => {
        setPageMessage({ text: '', type: '' });
        setIsCardActionLoading(true);

        if (auth.currentUser?.subscription?.planName === plan.tierName && plan.actionType === 'purchase') {
            setPageMessage({ text: `You are already subscribed to the ${plan.tierName} plan.`, type: 'info' });
            setIsCardActionLoading(false);
            return;
        }

        // This is the main logic block for purchase actions
        if (plan.actionType === 'purchase') {
            if (plan.paymentProcessor === 'lemonsqueezy' && (!plan.lemonSqueezyVariantId || plan.lemonSqueezyVariantId.includes('YOUR_VARIANT_ID'))) {
                 setPageMessage({
                    text: `This plan (${plan.tierName}) via Lemon Squeezy is currently not available (Configuration needed).`,
                    type: 'error'
                 });
                 setIsCardActionLoading(false);
                 return;
            }
            
            // *** THE CRITICAL FIX ***
            // 1. Set the data for the modal first.
            setSelectedPlanForModal(plan);
            // 2. Then, set the state to open the modal. This two-step process is reliable.
            setIsPaymentModalOpen(true);
            
            // 3. Stop the card's loading spinner, as the modal will now be the focus.
            setIsCardActionLoading(false);

        } else if (plan.actionType === 'info' || plan.actionType === 'signup') {
            if (plan.id === 'free_tier_01') {
                setPageMessage({ text: `You've selected the ${plan.tierName} plan. Redirecting...`, type: 'info' });
                // navigate('/signup'); // or '/dashboard' if appropriate
            }
            setIsCardActionLoading(false);
        } else {
            console.warn("[PricingPage] Unknown plan action type:", plan.actionType);
            setIsCardActionLoading(false);
        }
    }, [auth.currentUser, navigate]); // Dependencies for useCallback

    // **FIXED**: The handler to close the modal
    const handleClosePaymentModal = () => {
        // This function ensures a clean close.
        setIsPaymentModalOpen(false);
        setSelectedPlanForModal(null); // Clear the selected plan data
        setIsCardActionLoading(false); // Ensure the card buttons are re-enabled
    };

    return (
        <div className="min-h-screen relative text-white">
            <PageBackground />

            {/* Real-time update indicator */}
            {sseConnected && (
                <div className="fixed top-4 right-4 z-50 bg-green-900/80 border border-green-600 rounded-lg px-3 py-2 text-green-300 text-sm">
                    <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <span>Live pricing updates</span>
                    </div>
                </div>
            )}

            {isPageLoading && (
              <div className="fixed inset-0 bg-slate-900 bg-opacity-85 flex flex-col items-center justify-center z-[200]">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-sky-400 mb-4"></div>
                  <p className="text-lg text-sky-300">Processing...</p>
              </div>
            )}

            <PricingView
                pricingTiers={pricingTiers}
                onPlanSelect={handlePlanSelect}
                isViewLoading={isLoadingPlans}
                pageMessage={pageMessage}
                currentPlanName={auth.currentUser?.subscription?.planName}
                isCardActionLoading={isCardActionLoading}
            />

            {/* **FIXED**: More robust conditional rendering for the modal */}
            {/* This JSX now correctly waits for both the 'open' signal and the plan data to be ready */}
            {isPaymentModalOpen && selectedPlanForModal && (
                <PaymentModal
                    isOpen={isPaymentModalOpen}
                    onClose={handleClosePaymentModal}
                    title={`Confirm: ${selectedPlanForModal.tierName}`}
                    selectedPlan={selectedPlanForModal}
                />
            )}

            <footer className="text-center text-xs text-slate-500 pb-8 pt-4 relative z-10">
                <div className="flex flex-col items-center space-y-2">
                    <span>Payments processed securely. Manage your subscription in your account settings.</span>
                    {sseConnected && (
                        <span className="text-green-400 text-xs">
                            ✓ Real-time pricing updates enabled
                        </span>
                    )}
                </div>
            </footer>
        </div>
    );
};

PricingPage.displayName = 'PricingPage';

export default PricingPage;
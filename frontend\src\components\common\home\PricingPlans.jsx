// PricingPlans.jsx
import React from 'react';
import { Check } from 'lucide-react';
import useSubscriptionPlans from '../../../hooks/useSubscriptionPlans';

// Updated to use dynamic subscription plans
const PricingPlans = React.memo(({ onPlanSelect }) => {
  // Use dynamic subscription plans
  const { plans, loading, error } = useSubscriptionPlans();

  // Show loading state
  if (loading) {
    return (
      <section id="pricing" className="relative z-10 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-sky-400 mx-auto mb-4"></div>
            <p className="text-lg text-sky-300">Loading pricing plans...</p>
          </div>
        </div>
      </section>
    );
  }

  // Show error state
  if (error) {
    return (
      <section id="pricing" className="relative z-10 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <p className="text-lg text-red-400">Failed to load pricing plans: {error}</p>
          </div>
        </div>
      </section>
    );
  }

  // Transform plans for home page display
  const homePlans = plans.map(plan => ({
    name: plan.tierName,
    price: plan.price,
    description: plan.description,
    features: plan.features,
    planData: plan // Keep original plan data for onPlanSelect
  }));
  return (
    <section id="pricing" className="relative z-10 py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
            Simple Pricing
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Choose the perfect plan for your needs. Start free, upgrade when you're ready.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8 items-start">
          {homePlans.map((plan, index) => (
            <div
              key={index}
              className={`relative p-8 rounded-3xl border transition-all duration-300 ${
                plan.popular
                  ? 'bg-gradient-to-b from-purple-500/10 to-pink-500/10 border-purple-500/50 transform md:scale-105'
                  : 'bg-white/5 border-white/10 hover:border-white/20'
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-gradient-to-r from-purple-500 to-pink-500 px-4 py-1 rounded-full text-sm font-semibold">
                    Most Popular
                  </div>
                </div>
              )}

              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-white mb-2">{plan.name}</h3>
                <div className="text-4xl font-bold text-white mb-2">
                  {plan.price}
                  {plan.price !== "Free" && plan.price !== "Custom" && <span className="text-lg text-gray-400">/month</span>}
                </div>
                <p className="text-gray-300">{plan.description}</p>
              </div>

              <ul className="space-y-4 mb-8">
                {plan.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center">
                    <Check className="w-5 h-5 text-green-400 mr-3 flex-shrink-0" />
                    <span className="text-gray-300">{feature}</span>
                  </li>
                ))}
              </ul>

              <button
                onClick={() => onPlanSelect(plan.planData)}
                className={`w-full py-3 rounded-full font-semibold transition-all duration-300 ${
                plan.popular
                  ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:shadow-lg hover:shadow-purple-500/25'
                  : 'bg-white/10 text-white hover:bg-white/20 border border-white/20'
              }`}>
                {plan.price === "Free" ? "Get Started" : plan.price === "Custom" ? "Contact Sales" : "Choose Plan"}
              </button>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
});

export default PricingPlans;
// hooks/useSSE.js
import { useEffect, useRef, useState } from 'react';

/**
 * Custom hook for Server-Sent Events (SSE) connections
 * Provides real-time updates from the server
 */
export const useSSE = (url, options = {}) => {
    const [isConnected, setIsConnected] = useState(false);
    const [lastEvent, setLastEvent] = useState(null);
    const [error, setError] = useState(null);
    const eventSourceRef = useRef(null);
    const reconnectTimeoutRef = useRef(null);
    const reconnectAttempts = useRef(0);

    const {
        maxReconnectAttempts = 5,
        reconnectInterval = 3000,
        onEvent = () => {},
        onError = () => {},
        onConnect = () => {},
        onDisconnect = () => {},
        enabled = true
    } = options;

    const connect = () => {
        if (!enabled || eventSourceRef.current) return;

        try {
            console.log('SSE: Connecting to', url);
            const eventSource = new EventSource(url);
            eventSourceRef.current = eventSource;

            eventSource.onopen = () => {
                console.log('SSE: Connected');
                setIsConnected(true);
                setError(null);
                reconnectAttempts.current = 0;
                onConnect();
            };

            eventSource.onerror = (event) => {
                console.error('SSE: Connection error', event);
                setIsConnected(false);
                
                const errorMessage = 'SSE connection error';
                setError(errorMessage);
                onError(errorMessage);

                // Attempt to reconnect
                if (reconnectAttempts.current < maxReconnectAttempts) {
                    reconnectAttempts.current++;
                    console.log(`SSE: Attempting to reconnect (${reconnectAttempts.current}/${maxReconnectAttempts})`);
                    
                    reconnectTimeoutRef.current = setTimeout(() => {
                        disconnect();
                        connect();
                    }, reconnectInterval);
                } else {
                    console.log('SSE: Max reconnection attempts reached');
                    setError('Failed to establish SSE connection after multiple attempts');
                }
            };

            // Handle specific event types
            eventSource.addEventListener('connected', (event) => {
                const data = JSON.parse(event.data);
                console.log('SSE: Connection confirmed', data);
                setLastEvent({ type: 'connected', data });
                onEvent('connected', data);
            });

            eventSource.addEventListener('subscription-plan-updated', (event) => {
                const data = JSON.parse(event.data);
                console.log('SSE: Subscription plan updated', data);
                setLastEvent({ type: 'subscription-plan-updated', data });
                onEvent('subscription-plan-updated', data);
            });

            eventSource.addEventListener('subscription-plan-reset', (event) => {
                const data = JSON.parse(event.data);
                console.log('SSE: Subscription plan reset', data);
                setLastEvent({ type: 'subscription-plan-reset', data });
                onEvent('subscription-plan-reset', data);
            });

            eventSource.addEventListener('pricing-updated', (event) => {
                const data = JSON.parse(event.data);
                console.log('SSE: Pricing updated', data);
                setLastEvent({ type: 'pricing-updated', data });
                onEvent('pricing-updated', data);
            });

            eventSource.addEventListener('heartbeat', (event) => {
                const data = JSON.parse(event.data);
                // Don't log heartbeats to avoid spam
                setLastEvent({ type: 'heartbeat', data });
            });

        } catch (error) {
            console.error('SSE: Failed to create EventSource', error);
            setError('Failed to create SSE connection');
            onError(error.message);
        }
    };

    const disconnect = () => {
        if (reconnectTimeoutRef.current) {
            clearTimeout(reconnectTimeoutRef.current);
            reconnectTimeoutRef.current = null;
        }

        if (eventSourceRef.current) {
            console.log('SSE: Disconnecting');
            eventSourceRef.current.close();
            eventSourceRef.current = null;
            setIsConnected(false);
            onDisconnect();
        }
    };

    useEffect(() => {
        if (enabled) {
            connect();
        }

        return () => {
            disconnect();
        };
    }, [url, enabled]);

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            disconnect();
        };
    }, []);

    return {
        isConnected,
        lastEvent,
        error,
        connect,
        disconnect,
        reconnect: () => {
            disconnect();
            setTimeout(connect, 100);
        }
    };
};

export default useSSE;

// services/sseService.js
import EventEmitter from 'events';

/**
 * Server-Sent Events service for real-time updates
 * Manages client connections and broadcasts updates
 */
class SSEService extends EventEmitter {
    constructor() {
        super();
        this.clients = new Map(); // Store client connections
        this.setupEventHandlers();
    }

    /**
     * Setup internal event handlers
     */
    setupEventHandlers() {
        // Listen for subscription plan updates
        this.on('subscription-plan-updated', (data) => {
            this.broadcast('subscription-plan-updated', data);
        });

        this.on('subscription-plan-reset', (data) => {
            this.broadcast('subscription-plan-reset', data);
        });

        // Listen for pricing updates
        this.on('pricing-updated', (data) => {
            this.broadcast('pricing-updated', data);
        });
    }

    /**
     * Add a new SSE client connection
     */
    addClient(clientId, res) {
        console.log(`SSE: Adding client ${clientId}`);
        
        // Set SSE headers
        res.writeHead(200, {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Cache-Control'
        });

        // Store client connection
        this.clients.set(clientId, {
            response: res,
            connectedAt: new Date(),
            lastPing: new Date()
        });

        // Send initial connection confirmation
        this.sendToClient(clientId, 'connected', {
            message: 'Connected to real-time updates',
            timestamp: new Date().toISOString()
        });

        // Setup heartbeat
        const heartbeatInterval = setInterval(() => {
            if (this.clients.has(clientId)) {
                this.sendToClient(clientId, 'heartbeat', {
                    timestamp: new Date().toISOString()
                });
            } else {
                clearInterval(heartbeatInterval);
            }
        }, 30000); // Send heartbeat every 30 seconds

        // Handle client disconnect
        res.on('close', () => {
            console.log(`SSE: Client ${clientId} disconnected`);
            this.removeClient(clientId);
            clearInterval(heartbeatInterval);
        });

        res.on('error', (error) => {
            console.error(`SSE: Error with client ${clientId}:`, error);
            this.removeClient(clientId);
            clearInterval(heartbeatInterval);
        });
    }

    /**
     * Remove a client connection
     */
    removeClient(clientId) {
        if (this.clients.has(clientId)) {
            const client = this.clients.get(clientId);
            try {
                client.response.end();
            } catch (error) {
                console.error(`SSE: Error closing client ${clientId}:`, error);
            }
            this.clients.delete(clientId);
            console.log(`SSE: Removed client ${clientId}. Active clients: ${this.clients.size}`);
        }
    }

    /**
     * Send data to a specific client
     */
    sendToClient(clientId, event, data) {
        const client = this.clients.get(clientId);
        if (client) {
            try {
                const message = `event: ${event}\ndata: ${JSON.stringify(data)}\n\n`;
                client.response.write(message);
                client.lastPing = new Date();
            } catch (error) {
                console.error(`SSE: Error sending to client ${clientId}:`, error);
                this.removeClient(clientId);
            }
        }
    }

    /**
     * Broadcast data to all connected clients
     */
    broadcast(event, data) {
        console.log(`SSE: Broadcasting ${event} to ${this.clients.size} clients`);
        
        const message = {
            ...data,
            timestamp: new Date().toISOString()
        };

        // Send to all clients
        for (const [clientId, client] of this.clients) {
            this.sendToClient(clientId, event, message);
        }
    }

    /**
     * Get connection statistics
     */
    getStats() {
        return {
            totalClients: this.clients.size,
            clients: Array.from(this.clients.entries()).map(([id, client]) => ({
                id,
                connectedAt: client.connectedAt,
                lastPing: client.lastPing
            }))
        };
    }

    /**
     * Emit subscription plan update event
     */
    notifySubscriptionPlanUpdate(planName, planData) {
        this.emit('subscription-plan-updated', {
            planName,
            plan: planData,
            action: 'updated'
        });
    }

    /**
     * Emit subscription plan reset event
     */
    notifySubscriptionPlanReset(planName, planData) {
        this.emit('subscription-plan-reset', {
            planName,
            plan: planData,
            action: 'reset'
        });
    }

    /**
     * Emit pricing update event
     */
    notifyPricingUpdate(planName, pricing) {
        this.emit('pricing-updated', {
            planName,
            pricing,
            action: 'pricing-updated'
        });
    }

    /**
     * Clean up inactive clients
     */
    cleanupInactiveClients() {
        const now = new Date();
        const timeout = 5 * 60 * 1000; // 5 minutes

        for (const [clientId, client] of this.clients) {
            if (now - client.lastPing > timeout) {
                console.log(`SSE: Removing inactive client ${clientId}`);
                this.removeClient(clientId);
            }
        }
    }
}

// Create singleton instance
const sseService = new SSEService();

// Cleanup inactive clients every 2 minutes
setInterval(() => {
    sseService.cleanupInactiveClients();
}, 2 * 60 * 1000);

export default sseService;

// controllers/common/sseController.js
import sseService from '../../services/sseService.js';
import { v4 as uuidv4 } from 'uuid';

/**
 * Controller for Server-Sent Events (SSE) connections
 */

/**
 * Establish SSE connection for real-time updates
 */
export const connectSSE = (req, res) => {
    // Generate unique client ID
    const clientId = uuidv4();
    
    console.log(`SSE: New connection request from client ${clientId}`);
    
    // Add client to SSE service
    sseService.addClient(clientId, res);
};

/**
 * Get SSE connection statistics (admin only)
 */
export const getSSEStats = (req, res) => {
    try {
        const stats = sseService.getStats();
        res.json({
            success: true,
            stats
        });
    } catch (error) {
        console.error('SSE Stats error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get SSE statistics'
        });
    }
};

// controllers/common/subscriptionController.js
import SubscriptionLimits from '../../models/SubscriptionLimits.js';

/**
 * Public controller for subscription plan information
 * These endpoints are accessible to all users (no admin required)
 */

/**
 * Get all active subscription plans for public display
 * This endpoint provides pricing and feature information for the pricing page
 */
export const getPublicSubscriptionPlans = async (req, res) => {
    try {
        // Initialize default plans if they don't exist
        await SubscriptionLimits.initializeDefaultPlans();

        // Get all active plans sorted by order
        const plans = await SubscriptionLimits.find({ isActive: true })
            .sort({ sortOrder: 1 })
            .select('planName displayName description price limits sortOrder');

        // Transform the data for frontend consumption
        const publicPlans = plans.map(plan => ({
            id: plan._id,
            planName: plan.planName,
            displayName: plan.displayName,
            description: plan.description,
            price: plan.price,
            features: transformLimitsToFeatures(plan.limits),
            sortOrder: plan.sortOrder,
            // Add payment processor info based on plan
            paymentProcessor: plan.planName === 'Starter' ? null : 'paypal',
            actionType: plan.planName === 'Starter' ? 'info' : 'purchase',
            buttonText: plan.planName === 'Starter' ? 'Get Started' : 'Subscribe Now'
        }));

        res.json({ 
            success: true,
            plans: publicPlans,
            lastUpdated: new Date().toISOString()
        });
    } catch (error) {
        console.error('Get public subscription plans error:', error);
        res.status(500).json({ 
            success: false,
            error: 'Failed to fetch subscription plans' 
        });
    }
};

/**
 * Get specific subscription plan by name for public access
 */
export const getPublicSubscriptionPlan = async (req, res) => {
    try {
        const { planName } = req.params;

        const plan = await SubscriptionLimits.findOne({
            planName,
            isActive: true
        }).select('planName displayName description price limits sortOrder');

        if (!plan) {
            return res.status(404).json({ 
                success: false,
                error: 'Subscription plan not found' 
            });
        }

        const publicPlan = {
            id: plan._id,
            planName: plan.planName,
            displayName: plan.displayName,
            description: plan.description,
            price: plan.price,
            features: transformLimitsToFeatures(plan.limits),
            sortOrder: plan.sortOrder,
            paymentProcessor: plan.planName === 'Starter' ? null : 'paypal',
            actionType: plan.planName === 'Starter' ? 'info' : 'purchase',
            buttonText: plan.planName === 'Starter' ? 'Get Started' : 'Subscribe Now'
        };

        res.json({ 
            success: true,
            plan: publicPlan,
            lastUpdated: new Date().toISOString()
        });
    } catch (error) {
        console.error('Get public subscription plan error:', error);
        res.status(500).json({ 
            success: false,
            error: 'Failed to fetch subscription plan' 
        });
    }
};

/**
 * Transform subscription limits into user-friendly features list
 */
function transformLimitsToFeatures(limits) {
    const features = [];

    // PDF Uploads
    if (limits.pdfUploads?.monthly) {
        const monthly = limits.pdfUploads.monthly;
        features.push(monthly === -1 ? 'Unlimited PDF Uploads' : `${monthly} PDF Uploads/month`);
    }

    // Business Plans
    if (limits.businessPlans?.monthly) {
        const monthly = limits.businessPlans.monthly;
        features.push(monthly === -1 ? 'Unlimited Business Plans' : `${monthly} Business Plans/month`);
    }

    // Investor Pitches
    if (limits.investorPitches?.monthly) {
        const monthly = limits.investorPitches.monthly;
        features.push(monthly === -1 ? 'Unlimited Investor Pitches' : `${monthly} Investor Pitches/month`);
    }

    // Business Q&A
    if (limits.businessQA?.daily && limits.businessQA?.monthly) {
        const daily = limits.businessQA.daily;
        const monthly = limits.businessQA.monthly;
        if (daily === -1 && monthly === -1) {
            features.push('Unlimited Business Q&A');
        } else {
            features.push(`${daily === -1 ? 'Unlimited' : daily} Q&A/day, ${monthly === -1 ? 'Unlimited' : monthly}/month`);
        }
    }

    // Chat Messages
    if (limits.chatMessages?.daily && limits.chatMessages?.monthly) {
        const daily = limits.chatMessages.daily;
        const monthly = limits.chatMessages.monthly;
        if (daily === -1 && monthly === -1) {
            features.push('Unlimited Chat Messages');
        } else {
            features.push(`${daily === -1 ? 'Unlimited' : daily} Messages/day`);
        }
    }

    // Storage
    if (limits.storage?.total) {
        const storage = limits.storage.total;
        if (storage === -1) {
            features.push('Unlimited Storage');
        } else if (storage >= 1000) {
            features.push(`${storage / 1000} GB Storage`);
        } else {
            features.push(`${storage} MB Storage`);
        }
    }

    // Premium Features
    if (limits.advancedAnalytics) {
        features.push('Advanced Analytics');
    }
    if (limits.prioritySupport) {
        features.push('Priority Support');
    }
    if (limits.customBranding) {
        features.push('Custom Branding');
    }

    return features;
}

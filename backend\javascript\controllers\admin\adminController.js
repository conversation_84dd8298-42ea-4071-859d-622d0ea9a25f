// controllers/admin/adminController.js
import User from '../../models/User.js';
import AdminSettings from '../../models/AdminSettings.js';
import SubscriptionLimits from '../../models/SubscriptionLimits.js';
import sseService from '../../services/sseService.js';

/**
 * Get all users for admin dashboard
 */
export const getAllUsers = async (req, res) => {
    try {
        const { page = 1, limit = 10, search = '', sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
        
        // Build search query
        const searchQuery = search ? {
            $or: [
                { name: { $regex: search, $options: 'i' } },
                { email: { $regex: search, $options: 'i' } }
            ]
        } : {};

        // Calculate pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);
        
        // Build sort object
        const sort = {};
        sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

        // Get users with pagination
        const users = await User.find(searchQuery)
            .select('-password') // Exclude password field
            .sort(sort)
            .skip(skip)
            .limit(parseInt(limit));

        // Get total count for pagination
        const totalUsers = await User.countDocuments(searchQuery);
        const totalPages = Math.ceil(totalUsers / parseInt(limit));

        res.json({
            users,
            pagination: {
                currentPage: parseInt(page),
                totalPages,
                totalUsers,
                hasNextPage: page < totalPages,
                hasPrevPage: page > 1
            }
        });
    } catch (error) {
        console.error('Get all users error:', error);
        res.status(500).json({ error: 'Failed to fetch users' });
    }
};

/**
 * Get user by ID
 */
export const getUserById = async (req, res) => {
    try {
        const { id } = req.params;
        const user = await User.findById(id).select('-password');
        
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }

        res.json({ user });
    } catch (error) {
        console.error('Get user by ID error:', error);
        res.status(500).json({ error: 'Failed to fetch user' });
    }
};

/**
 * Update user (admin can update any user)
 */
export const updateUser = async (req, res) => {
    try {
        const { id } = req.params;
        const { name, email, isAdmin, isVerified } = req.body;

        // Validate ObjectId format
        if (!id.match(/^[0-9a-fA-F]{24}$/)) {
            return res.status(400).json({ error: 'Invalid user ID format' });
        }

        // Prevent admin from removing their own admin status
        if (id === req.user._id.toString() && isAdmin === false) {
            return res.status(400).json({
                error: 'You cannot remove your own admin privileges'
            });
        }

        // Find user and check if exists
        const user = await User.findById(id);
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }

        // Store original values for logging
        const originalValues = {
            name: user.name,
            email: user.email,
            isAdmin: user.isAdmin,
            isVerified: user.isVerified
        };

        // Update user fields only if provided
        if (name !== undefined && name !== user.name) user.name = name;
        if (email !== undefined && email !== user.email) user.email = email;
        if (isAdmin !== undefined && isAdmin !== user.isAdmin) user.isAdmin = isAdmin;
        if (isVerified !== undefined && isVerified !== user.isVerified) user.isVerified = isVerified;

        // Save changes and wait for completion
        await user.save();

        // Verify the update by fetching fresh data
        const updatedUser = await User.findById(id).select('-password');

        if (!updatedUser) {
            return res.status(500).json({ error: 'User update verification failed' });
        }

        // Log the update for audit purposes
        console.log(`Admin ${req.user.email} updated user ${originalValues.email} (ID: ${id})`);
        console.log('Changes:', {
            name: originalValues.name !== updatedUser.name ? `${originalValues.name} → ${updatedUser.name}` : 'unchanged',
            email: originalValues.email !== updatedUser.email ? `${originalValues.email} → ${updatedUser.email}` : 'unchanged',
            isAdmin: originalValues.isAdmin !== updatedUser.isAdmin ? `${originalValues.isAdmin} → ${updatedUser.isAdmin}` : 'unchanged',
            isVerified: originalValues.isVerified !== updatedUser.isVerified ? `${originalValues.isVerified} → ${updatedUser.isVerified}` : 'unchanged'
        });

        res.json({
            user: updatedUser,
            message: 'User updated successfully',
            changes: {
                name: originalValues.name !== updatedUser.name,
                email: originalValues.email !== updatedUser.email,
                isAdmin: originalValues.isAdmin !== updatedUser.isAdmin,
                isVerified: originalValues.isVerified !== updatedUser.isVerified
            }
        });
    } catch (error) {
        console.error('Update user error:', error);
        if (error.code === 11000) {
            // Duplicate key error (email already exists)
            const field = Object.keys(error.keyValue)[0];
            res.status(400).json({ error: `${field} already exists` });
        } else if (error.name === 'ValidationError') {
            // Mongoose validation error
            const messages = Object.values(error.errors).map(e => e.message);
            res.status(400).json({ error: messages.join(', ') });
        } else {
            res.status(500).json({ error: 'Failed to update user' });
        }
    }
};

/**
 * Delete user (admin can delete any user except themselves)
 */
export const deleteUser = async (req, res) => {
    try {
        const { id } = req.params;

        // Validate ObjectId format
        if (!id.match(/^[0-9a-fA-F]{24}$/)) {
            return res.status(400).json({ error: 'Invalid user ID format' });
        }

        // Prevent admin from deleting themselves
        if (id === req.user._id.toString()) {
            return res.status(400).json({
                error: 'You cannot delete your own account'
            });
        }

        // Check if user exists before deletion
        const user = await User.findById(id);
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }

        // Perform the deletion and wait for completion
        const deletedUser = await User.findByIdAndDelete(id);

        if (!deletedUser) {
            return res.status(500).json({ error: 'Failed to delete user from database' });
        }

        // Log the deletion for audit purposes
        console.log(`Admin ${req.user.email} deleted user ${user.email} (ID: ${id})`);

        res.json({
            message: 'User deleted successfully',
            deletedUser: {
                id: deletedUser._id,
                email: deletedUser.email,
                name: deletedUser.name
            }
        });
    } catch (error) {
        console.error('Delete user error:', error);
        res.status(500).json({ error: 'Failed to delete user' });
    }
};

/**
 * Get admin users only
 */
export const getAdminUsers = async (req, res) => {
    try {
        const adminUsers = await User.find({ isAdmin: true })
            .select('-password')
            .sort({ createdAt: -1 });

        res.json({ adminUsers });
    } catch (error) {
        console.error('Get admin users error:', error);
        res.status(500).json({ error: 'Failed to fetch admin users' });
    }
};

/**
 * Get system analytics
 */
export const getSystemAnalytics = async (req, res) => {
    try {
        // Get user statistics
        const totalUsers = await User.countDocuments();
        const verifiedUsers = await User.countDocuments({ isVerified: true });
        const adminUsers = await User.countDocuments({ isAdmin: true });
        const unverifiedUsers = totalUsers - verifiedUsers;

        // Get registration statistics for the last 30 days
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        
        const recentRegistrations = await User.countDocuments({
            createdAt: { $gte: thirtyDaysAgo }
        });

        // Get admin settings
        const adminSettings = await AdminSettings.getSettings();

        res.json({
            userStats: {
                totalUsers,
                verifiedUsers,
                unverifiedUsers,
                adminUsers,
                recentRegistrations
            },
            systemInfo: {
                hasInitialAdmin: adminSettings.hasInitialAdmin,
                systemAdminEmail: adminSettings.systemAdminEmail,
                allowNewAdminRegistration: adminSettings.allowNewAdminRegistration,
                maintenanceMode: adminSettings.maintenanceMode
            }
        });
    } catch (error) {
        console.error('Get system analytics error:', error);
        res.status(500).json({ error: 'Failed to fetch system analytics' });
    }
};

/**
 * Update system settings
 */
export const updateSystemSettings = async (req, res) => {
    try {
        const { allowNewAdminRegistration, maintenanceMode, systemName } = req.body;
        
        const settings = await AdminSettings.getSettings();
        
        if (allowNewAdminRegistration !== undefined) {
            settings.allowNewAdminRegistration = allowNewAdminRegistration;
        }
        if (maintenanceMode !== undefined) {
            settings.maintenanceMode = maintenanceMode;
        }
        if (systemName !== undefined) {
            settings.systemName = systemName;
        }

        await settings.save();
        
        res.json({ 
            settings, 
            message: 'System settings updated successfully' 
        });
    } catch (error) {
        console.error('Update system settings error:', error);
        res.status(500).json({ error: 'Failed to update system settings' });
    }
};

/**
 * Check if admin registration is allowed
 */
export const checkAdminRegistrationStatus = async (req, res) => {
    try {
        const canRegister = await AdminSettings.canRegisterAdmin();
        const settings = await AdminSettings.getSettings();

        res.json({
            canRegisterAdmin: canRegister,
            hasInitialAdmin: settings.hasInitialAdmin
        });
    } catch (error) {
        console.error('Check admin registration status error:', error);
        res.status(500).json({ error: 'Failed to check admin registration status' });
    }
};

/**
 * Get all subscription plans and their limits
 */
export const getSubscriptionPlans = async (req, res) => {
    try {
        // Initialize default plans if they don't exist
        await SubscriptionLimits.initializeDefaultPlans();

        const plans = await SubscriptionLimits.find({ isActive: true })
            .sort({ sortOrder: 1 });

        res.json({ plans });
    } catch (error) {
        console.error('Get subscription plans error:', error);
        res.status(500).json({ error: 'Failed to fetch subscription plans' });
    }
};

/**
 * Get specific subscription plan by name
 */
export const getSubscriptionPlan = async (req, res) => {
    try {
        const { planName } = req.params;

        const plan = await SubscriptionLimits.findOne({
            planName,
            isActive: true
        });

        if (!plan) {
            return res.status(404).json({ error: 'Subscription plan not found' });
        }

        res.json({ plan });
    } catch (error) {
        console.error('Get subscription plan error:', error);
        res.status(500).json({ error: 'Failed to fetch subscription plan' });
    }
};

/**
 * Update subscription plan limits
 */
export const updateSubscriptionPlan = async (req, res) => {
    try {
        const { planName } = req.params;
        const updateData = req.body;

        const plan = await SubscriptionLimits.findOne({ planName });

        if (!plan) {
            return res.status(404).json({ error: 'Subscription plan not found' });
        }

        // Update plan data
        if (updateData.displayName) plan.displayName = updateData.displayName;
        if (updateData.description !== undefined) plan.description = updateData.description;
        if (updateData.price) plan.price = { ...plan.price, ...updateData.price };
        if (updateData.limits) plan.limits = { ...plan.limits, ...updateData.limits };
        if (updateData.isActive !== undefined) plan.isActive = updateData.isActive;
        if (updateData.sortOrder !== undefined) plan.sortOrder = updateData.sortOrder;

        await plan.save();

        // Emit SSE event for real-time updates
        sseService.notifySubscriptionPlanUpdate(planName, plan);

        res.json({
            plan,
            message: `${planName} plan updated successfully`
        });
    } catch (error) {
        console.error('Update subscription plan error:', error);
        res.status(500).json({ error: 'Failed to update subscription plan' });
    }
};

/**
 * Reset subscription plan to default values
 */
export const resetSubscriptionPlan = async (req, res) => {
    try {
        const { planName } = req.params;

        const defaultData = SubscriptionLimits.getDefaultLimits(planName);

        const plan = await SubscriptionLimits.findOneAndUpdate(
            { planName },
            {
                ...defaultData,
                planName // Ensure planName is preserved
            },
            {
                new: true,
                upsert: true
            }
        );

        // Emit SSE event for real-time updates
        sseService.notifySubscriptionPlanReset(planName, plan);

        res.json({
            plan,
            message: `${planName} plan reset to default values`
        });
    } catch (error) {
        console.error('Reset subscription plan error:', error);
        res.status(500).json({ error: 'Failed to reset subscription plan' });
    }
};

/**
 * Get subscription usage statistics
 */
export const getSubscriptionStats = async (req, res) => {
    try {
        // Get user counts by plan
        const userStats = await User.aggregate([
            {
                $group: {
                    _id: '$subscription.planName',
                    count: { $sum: 1 },
                    verified: {
                        $sum: { $cond: ['$isVerified', 1, 0] }
                    }
                }
            }
        ]);

        // Get total usage statistics
        const totalUsers = await User.countDocuments();
        const activeUsers = await User.countDocuments({ isVerified: true });

        // Calculate revenue estimates (this would be more complex in a real system)
        const plans = await SubscriptionLimits.find({ isActive: true });
        let estimatedMonthlyRevenue = 0;

        for (const stat of userStats) {
            const plan = plans.find(p => p.planName === stat._id);
            if (plan && plan.price.monthly > 0) {
                estimatedMonthlyRevenue += stat.verified * plan.price.monthly;
            }
        }

        res.json({
            userStats,
            totalUsers,
            activeUsers,
            estimatedMonthlyRevenue,
            planDistribution: userStats.map(stat => ({
                planName: stat._id || 'Starter',
                userCount: stat.count,
                verifiedUsers: stat.verified
            }))
        });
    } catch (error) {
        console.error('Get subscription stats error:', error);
        res.status(500).json({ error: 'Failed to fetch subscription statistics' });
    }
};

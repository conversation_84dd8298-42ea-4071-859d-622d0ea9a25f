// src/components/admin/AdminSidebar.jsx
import React from 'react';
import {
  FiUsers,
  FiShield,
  FiSettings,
  FiBarChart,
  FiCreditCard,
  FiHome,
  FiLogOut,
  FiX
} from 'react-icons/fi';
import { useAuth } from '../../context/AuthContext';
import { useNavigate } from 'react-router-dom';

const AdminSidebar = ({ activeTab, setActiveTab, isMobileOpen = false, setIsMobileOpen }) => {
  const { logout, currentUser } = useAuth();
  const navigate = useNavigate();

  const navigationTabs = [
    { id: 'users', label: 'User Management', icon: FiUsers },
    { id: 'admins', label: 'Admin Management', icon: FiShield },
    { id: 'payments', label: 'Payment Management', icon: FiCreditCard },
    { id: 'analytics', label: 'Analytics', icon: FiBarChart },
    { id: 'settings', label: 'System Settings', icon: FiSettings },
  ];

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const handleGoHome = () => {
    navigate('/');
  };

  return (
    <>
      {/* Mobile Overlay */}
      {isMobileOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden"
          onClick={() => setIsMobileOpen && setIsMobileOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`w-64 bg-slate-800 border-r border-slate-700 flex-shrink-0 fixed left-0 top-0 h-screen z-40 transform transition-transform duration-300 ease-in-out ${
        isMobileOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'
      }`}>
      <div className="flex flex-col h-full">
        {/* Sidebar Header */}
        <div className="flex-shrink-0 p-6 border-b border-slate-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <FiShield className="w-8 h-8 text-purple-500 mr-3" />
              <div>
                <h1 className="text-lg font-bold text-white">Admin Panel</h1>
                <p className="text-xs text-slate-400">DOSKY Dashboard</p>
              </div>
            </div>
            {/* Mobile Close Button */}
            {setIsMobileOpen && (
              <button
                onClick={() => setIsMobileOpen(false)}
                className="lg:hidden text-slate-400 hover:text-white p-1"
              >
                <FiX className="w-5 h-5" />
              </button>
            )}
          </div>
        </div>

        {/* User Info */}
        <div className="flex-shrink-0 p-4 border-b border-slate-700">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center">
              <span className="text-white text-sm font-medium">
                {(currentUser?.name || currentUser?.email || 'A').charAt(0).toUpperCase()}
              </span>
            </div>
            <div className="ml-3 flex-1 min-w-0">
              <p className="text-sm font-medium text-white truncate">
                {currentUser?.name || 'Admin User'}
              </p>
              <p className="text-xs text-slate-400 truncate">
                {currentUser?.email || '<EMAIL>'}
              </p>
            </div>
          </div>
        </div>

        {/* Navigation Links */}
        <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
          {navigationTabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-all duration-200 group ${
                  activeTab === tab.id
                    ? 'bg-purple-600 text-white shadow-lg'
                    : 'text-slate-300 hover:bg-slate-700 hover:text-white'
                }`}
              >
                <Icon className={`w-5 h-5 mr-3 transition-transform duration-200 ${
                  activeTab === tab.id ? 'scale-110' : 'group-hover:scale-105'
                }`} />
                <span className="font-medium">{tab.label}</span>
              </button>
            );
          })}
        </nav>

        {/* Bottom Actions */}
        <div className="flex-shrink-0 p-4 border-t border-slate-700 space-y-2">
          <button
            onClick={handleGoHome}
            className="w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors text-slate-300 hover:bg-slate-700 hover:text-white"
          >
            <FiHome className="w-5 h-5 mr-3" />
            <span className="font-medium">Back to Home</span>
          </button>
          
          <button
            onClick={handleLogout}
            className="w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors text-red-400 hover:bg-red-900/20 hover:text-red-300"
          >
            <FiLogOut className="w-5 h-5 mr-3" />
            <span className="font-medium">Sign Out</span>
          </button>
        </div>

        {/* Footer */}
        <div className="flex-shrink-0 p-4 border-t border-slate-700">
          <div className="text-center">
            <p className="text-xs text-slate-500">DOSKY Admin v1.0</p>
            <p className="text-xs text-slate-600 mt-1">© 2024 All rights reserved</p>
          </div>
        </div>
      </div>
      </div>
    </>
  );
};

export default AdminSidebar;

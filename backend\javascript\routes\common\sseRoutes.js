// routes/common/sseRoutes.js
import express from 'express';
import { connectSSE, getSSEStats } from '../../controllers/common/sseController.js';
import { protect } from '../../middleware/authMiddleware.js';
import { requireAdmin } from '../../middleware/adminMiddleware.js';

const router = express.Router();

/**
 * SSE Routes for real-time updates
 */

// Public SSE connection endpoint (no auth required for real-time updates)
router.get('/connect', connectSSE);

// Admin-only endpoint to get SSE statistics
router.get('/stats', protect, requireAdmin, getSSEStats);

export default router;

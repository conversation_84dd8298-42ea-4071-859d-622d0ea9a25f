// src/pages/admin/AdminDashboard.jsx
import React, { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import { Navigate } from 'react-router-dom';
import {
  FiShield,
  <PERSON>Loader,
  FiAlertCircle,
  FiMenu
} from 'react-icons/fi';

// Import dashboard components
import UserManagement from '../../components/admin/UserManagement';
import AdminManagement from '../../components/admin/AdminManagement';
import SystemSettings from '../../components/admin/SystemSettings';
import Analytics from '../../components/admin/Analytics';
import PaymentManagement from '../../components/admin/PaymentManagement';
import AdminSidebar from '../../components/admin/AdminSidebar';

const AdminDashboard = () => {
  const { currentUser, isAuthenticated, isLoading } = useAuth();
  const [activeTab, setActiveTab] = useState('users');
  const [dashboardLoading, setDashboardLoading] = useState(true);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDashboardLoading(false);
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  if (isLoading || dashboardLoading) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <FiLoader className="w-12 h-12 text-purple-500 animate-spin mx-auto mb-4" />
          <p className="text-slate-300">Loading Admin Dashboard...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <Navigate to="/" replace />;
  }

  if (!currentUser?.isAdmin) {
    return (
      <div className="h-screen relative bg-slate-900 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <FiAlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-white mb-2">Access Denied</h1>
          <p className="text-slate-400 mb-6">
            You don't have administrator privileges to access this page.
          </p>
          <button
            onClick={() => window.history.back()}
            className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }



  const renderTabContent = () => {
    switch (activeTab) {
      case 'users': return <UserManagement />;
      case 'admins': return <AdminManagement />;
      case 'payments': return <PaymentManagement />;
      case 'analytics': return <Analytics />;
      case 'settings': return <SystemSettings />;
      default: return <UserManagement />;
    }
  };

  return (
    <div className="h-screen bg-slate-900 flex overflow-hidden">
      {/* Fixed Sidebar */}
      <AdminSidebar
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        isMobileOpen={isMobileMenuOpen}
        setIsMobileOpen={setIsMobileMenuOpen}
      />

      {/* Main Content Area */}
      <div className="flex-1 lg:ml-64 flex flex-col h-full">
        

        {/* Content Area with Scrolling */}
        <div className="flex-1 overflow-y-auto">
          <div className="bg-slate-900 min-h-full">
            {renderTabContent()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;